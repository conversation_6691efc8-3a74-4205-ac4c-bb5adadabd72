using lep.configuration;
using lep.despatch;
using lep.job;
using lep.job.impl;
using Serilog;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Printing;
using System.Drawing.Text;
using System.Linq;
using System.Windows.Forms;

namespace lep.despatch.impl.label
{
    /// <summary>
    /// Unified Further Processing Label that can handle all label types using the data extractor
    /// Supports both single job and multiple jobs (List<IJob>) following FurtherPocessingThumbnailListLabel pattern
    /// </summary>
    public class UnifiedFurtherProcessingLabel : BaseFurtherProcessingLabel
    {
        private FurtherProcessingLabelData _labelData;
        private LabelType _labelType;

        #region Properties

        public IList<IJob> Jobs { get; set; }

        public override IJob Job
        {
            get { return job; }
            set
            {
                job = value;
                FormatPrintContent();
            }
        }

        #endregion Properties

        #region Constructors

        // Constructor for single job (backward compatibility)
        public UnifiedFurtherProcessingLabel(IJob job, LabelType labelType,
			string printerAndTray,
			IConfigurationApplication configurationApplication, string filename)
        {
            _labelType = labelType;
            this.Jobs = new List<IJob> { job };
            this.Job = job;
            this.PrinterAndTray = printerAndTray;
            this.ConfigurationApplication = configurationApplication;
            this.PrintFileName = filename;
			FormatPrintContent(); // Initialize label data
		}

        // Constructor for multiple jobs (following FurtherPocessingThumbnailListLabel pattern)
        public UnifiedFurtherProcessingLabel(IList<IJob> jobs, LabelType labelType,
            string printerAndTray,
            IConfigurationApplication configurationApplication, string filename)
        {
            _labelType = labelType;
            this.Jobs = jobs;
            this.PrinterAndTray = printerAndTray;
            this.ConfigurationApplication = configurationApplication;
            this.PrintFileName = filename;
        }

        #endregion Constructors

        #region Additional Properties

        public LabelType LabelType
        {
            get => _labelType;
            set
            {
                _labelType = value;
                if (Job != null)
                {
                    FormatPrintContent(); // Refresh data when label type changes
                }
            }
        }

        public FurtherProcessingLabelData LabelData => _labelData;

        #endregion Additional Properties

        #region Protected Methods

        private int currentJob = 0;

        protected override void OnPrintPage(PrintPageEventArgs e)
        {
            // Handle multiple jobs like FurtherPocessingThumbnailListLabel
            if (!specialInstructionsOnNextPage && Jobs != null && Jobs.Count > 0)
            {
                Job = Jobs[currentJob++];
                pageNumber = 0;
            }

            base.OnPrintPage(e);
            var g = e.Graphics;

            g.SmoothingMode = SmoothingMode.None;
            g.TextRenderingHint = TextRenderingHint.SingleBitPerPixel;
            g.PixelOffsetMode = PixelOffsetMode.None;
            g.CompositingQuality = CompositingQuality.HighQuality;
            g.InterpolationMode = InterpolationMode.HighQualityBicubic;

            pageNumber++;
            if (pageNumber == 1)
            {
                #region title

                var title = Job.GetJobTemplateName();
                g.DrawString(title, titleFont, Brushes.Black, rTitle, titleAlignment);

                #endregion title

                #region print further processing instruction in top part

                var strMiddle = processingText.ToString();

                var hMiddle = (int)g.MeasureString(strMiddle, defaultFont).Height;
                var rMiddle = new Rectangle(left, top + hTitle + 5, width, (266 - top - hTitle - 5));
                g.DrawString(strMiddle, defaultFont, Brushes.Black, rMiddle, middleFormat);

                #endregion print further processing instruction in top part

                #region draw basic info like cust, job, order

                var basicJobInformation = basicInformation.ToString();

                var hBasicInfo = (int)g.MeasureString(basicJobInformation, defaultFont).Height;
                g.DrawString(basicJobInformation, defaultFont, Brushes.Black, rBasicInfoTxt, middleFormat);

                #endregion draw basic info like cust, job, order

                DrawRoundedCorndersBox(g);
                DrawThumbnail(g);
                DrawEDD(g);
                DrawBarcode(g);
            }

            #region draw special instructions

            DrawSpecialInstructions(e, g);

            #endregion draw special instructions

            // Handle multiple jobs pagination like FurtherPocessingThumbnailListLabel
            if (Jobs != null && currentJob == Jobs.Count && !specialInstructionsOnNextPage)
                e.HasMorePages = false;
            else
            {
                e.HasMorePages = true;
            }
        }

        #endregion Protected Methods

        #region Public Methods

        public new void FormatPrintContent()
        {
            basicInformation.Clear();
            processingText.Clear();
            strInstructions = "";

            try
            {
                if (Job == null) return;

                // Use the data extractor to get all job information
                _labelData = FurtherProcessingLabelDataExtractor.ExtractJobData(Job, _labelType);

                // Strings to Print from Job Properties - following FurtherPocessingThumbnailListLabel pattern
                basicInformation.AppendFormat(fmt, "Job Name", Job.Name).AppendLine();
                basicInformation.AppendFormat(fmt, "Order #", Job.Order.OrderNr).AppendLine();
                basicInformation.AppendFormat(fmt, "Job # ", Job.JobNr).AppendLine();

                if (Job.ReOrderSourceJobId != 0)
                {
                    basicInformation.AppendFormat(fmt, "Orig Job #", Job.ReOrderSourceJobId.ToString()).AppendLine();
                }

                basicInformation.AppendFormat(fmt, "Courier", Job.Order.Courier).AppendLine();
                processingText.AppendFormat(fmt, "Courier", Job.Order.Courier).AppendLine();

                if (Job.Order.Jobs.Count() > 1)
                {
                    basicInformation.Append("*** Multi Job Order ***");
                }

                if (Job.SendSamples == true)
                {
                    processingText.AppendLine("*** Send Samples ***");
                }

                if (Job.Runs.Count > 0)
                {
                    processingText.AppendFormat(fmt, "Run #", Job.Runs[0].RunNr).AppendLine();
                }

                processingText.AppendFormat(fmt, "Size", Job.GetJobSize()).AppendLine();

                // Add processing instructions from data extractor
                if (!string.IsNullOrEmpty(_labelData.ProcessingText1))
                {
                    processingText.AppendLine(_labelData.ProcessingText1);
                }
                if (!string.IsNullOrEmpty(_labelData.ProcessingText2))
                {
                    processingText.AppendLine(_labelData.ProcessingText2);
                }

                strInstructions = _labelData.SpecialInstructionsText;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error formatting print content for job {JobId}", Job?.Id);
                throw;
            }
        }

        /// <summary>
        /// Get specific formatted section text
        /// </summary>
        /// <param name="section">Section name (BasicInformation, ProcessingText, SpecialInstructions)</param>
        /// <returns>Formatted text for the section</returns>
        public string GetFormattedSection(string section)
        {
            if (_labelData == null || Job == null)
                return string.Empty;

            return FurtherProcessingLabelDataExtractor.GetFormattedSection(Job, _labelType, section);
        }

        /// <summary>
        /// Show print preview dialog for this label
        /// </summary>
        public void ShowPreview()
        {
            using (var printPreviewDialog = new System.Windows.Forms.PrintPreviewDialog())
            {
                printPreviewDialog.Document = this;
                printPreviewDialog.ShowDialog();
            }
        }

        #endregion Public Methods
    }
}
